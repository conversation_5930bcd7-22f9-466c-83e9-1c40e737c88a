<template>
  <div>
    <!-- 添加右上角关闭按钮 -->
    <div
      class="fixed right-12px top-12px z-50 w-30px h-30px bg-[rgba(0,0,0,0.3)] rounded-full flex-cc"
      @click="handleClose"
    >
      <g-icon
        class="relative top-[1px]"
        name="ri-close-line"
        size="20"
        color="#fff"
      />
    </div>

    <FirstScreen
      v-if="once"
      :report-data="reportData"
      @change-status="handleFirstScreenChange"
    />
    <div v-else class="gradient-bg overflow-x-hidden">
      <template v-if="!showEmpty && reportData">
        <!-- 顶部背景，移出内容区域以便最大拉伸 -->
        <div class="w-100vw h-156px bg-top-bg"></div>
      </template>

      <!-- 内容容器，在pad横屏时固定宽度 -->
      <div class="content-container">
        <template v-if="!showEmpty && reportData">
          <OneCard :report-data="reportData"></OneCard>
          <TwoCard :report-data="reportData"></TwoCard>
          <ThreeCard :report-data="reportData"></ThreeCard>
          <FourCard :report-data="reportData"></FourCard>
          <!-- 寄语 -->
          <div
            class="bg-[#fff] rounded-[8px] mx-12px p-19px text-13px text-[#2E2323] mb-36px"
          >
            <div class="message-title flex-cc text-[#000340] mx-auto mb-19px">
              老师寄语
            </div>
            <div class="text-14px text-[#666]">
              {{ reportData?.encouragementWords?.join('') || '无' }}
            </div>
          </div>
          <div class="footer flex-cc">
            <div
              class="bg-#EEEEFF w-125px h-40px flex-cc border border-[#E5E7EB] rounded-[7px] mr-13px"
              @click="reTest"
            >
              重新测验
            </div>
            <!-- <div
              class="bg-#B3BFF7 w-165px h-40px bg-[#6365FF] rounded-[7px] text-white flex-cc"
              @click="goToStudyPath"
            >
              进入最优学习路径
              <g-icon name="ri-arrow-right-line ml-6px" size="16" color="#fff" />
            </div> -->
          </div>
        </template>

        <g-empty v-show="showEmpty" textColor="#fff"></g-empty>
        <g-loading v-show="showLoading" class="h-200px"></g-loading>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import FirstScreen from './components/FirstScreen/index.vue'
import OneCard from './components/OneCard.vue'
import TwoCard from './components/TwoCard.vue'
import ThreeCard from './components/ThreeCard.vue'
import FourCard from './components/FourCard.vue'
import { getOneCaseReportDetail } from '@/api/oneCaseReport'
import type { IOneCaseReportDetail } from './type'

/** 是否第一次进入,写死 */
let once = $ref(false)

let showLoading = $ref(true)
let showEmpty = $ref(false)

/** 报告详情数据 */
let reportData = $ref<IOneCaseReportDetail | null>(null)

const route = useRoute()

/** 获取一生一案报告详情 */
async function getOneCaseReportDetailApi(): Promise<void> {
  try {
    showLoading = true
    showEmpty = false
    const res = await getOneCaseReportDetail(
      Number(route.query.exerciseTaskReportId),
    )

    if (res && Object.keys(res).length > 0) {
      reportData = res
      showEmpty = false
    } else {
      reportData = null
      showEmpty = true
    }
  } catch (error) {
    console.error('获取报告详情失败:', error)
    reportData = null
    showEmpty = true
  } finally {
    showLoading = false
  }
}

onMounted(() => {
  getOneCaseReportDetailApi()
})

/** 处理首屏状态改变 */
function handleFirstScreenChange(): void {
  once = false
}

/** 重新测验 */
const reTest = $g._.debounce(
  () => {
    const url = `${import.meta.env.VITE_APP_THREE_LANDSCAPE_URL}/#/student/lifeCase/main?type=restart`
    if ($g.inApp) {
      setFlutterParam(url, true, false)
    } else {
      window.location.href = url
    }
  },
  0,
  {
    leading: true,
    trailing: false,
  },
)

/** 进入最优学习路径 */
const goToStudyPath = $g._.debounce(
  () => {
    // TODO: 实现跳转到最优学习路径的逻辑,暂无url
    console.log('跳转到最优学习路径')
  },
  500,
  {
    leading: true,
    trailing: false,
  },
)

/** 安卓跳转 */
async function setFlutterParam(
  url: string,
  landscape: boolean,
  refreshCallJs: boolean,
  callback?: () => void,
): Promise<void> {
  let option: any = {
    url,
    refreshCallJs: refreshCallJs || false,
  }
  //横屏参数
  if (landscape) {
    option = {
      url,
      refreshCallJs,
      inSafeArea: {
        top: false,
        left: true,
        bottom: false,
        right: false,
      },
      beforeEnter: {
        orientation: {
          portraitUp: true,
          portraitDown: false,
          landscapeLeft: false,
          landscapeRight: false,
        },
        fullPage: false,
      },
      afterEnter: {
        orientation: {
          portraitUp: false,
          portraitDown: false,
          landscapeLeft: true,
          landscapeRight: false,
        },
        fullPage: true,
      },
    }
  }
  await $g.flutter('launchInNewWebView2', option)
}

/** 关闭页面 */
function handleClose() {
  $g.flutter('back', { removeAllWeb: true })
}
</script>

<style lang="scss" scoped>
.gradient-bg {
  background: linear-gradient(
    to right,
    #719efd 0%,
    #e6e8fe 25%,
    #eeeeff 50%,
    #abbff9 50%,
    #e6e8fe 75%,
    #eeeeff 100%
  );
  /* 为了更好的兼容性，可以添加备用背景色 */
  background-color: #b3bff7;
  /* 设置高度以便看到效果 */
  height: 100vh;
  overflow: auto;
  padding-bottom: 46px;
  &::-webkit-scrollbar {
    display: none;
  }
}

/* 顶部背景样式 */
.bg-top-bg {
  background: url('@/assets/img/oneCaseReport/top-bg.png') no-repeat center
    center;
  background-size: 100% 100%;
}

/* 内容容器样式 */
.content-container {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

:deep() {
  .message-title {
    background: url('@/assets/img/oneCaseReport/title.png') no-repeat center
      center;
    background-size: 100% 100%;
    width: 197px;
    height: 27px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: #000340;
    margin: 0 auto 19px;
  }
}

:deep() {
  .my-table {
    font-size: 11px !important; // 整个表格字体大小11px
  }
  .thead {
    font-size: 11px !important;
  }
  .tr-row {
    :nth-child(3) {
      text-align: left;
    }
  }
  .tr-row.stripe {
    &:nth-child(odd) {
      background: #ffffff; // 奇数行白色
    }
    &:nth-child(even) {
      background: #f4f5fe; // 偶数行你要的深色
    }
  }
}
</style>
